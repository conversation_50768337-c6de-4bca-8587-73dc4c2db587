/* eslint-disable @typescript-eslint/no-unused-vars */
import { useRef, useImperativeHandle, useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import AuthContext from './AuthContext'
import appConfig from '@/configs/app.config'
import { apiGetUserOrganization, apiSignIn } from '@/services/AuthService'
import { REDIRECT_URL_KEY } from '@/constants/app.constant'
import { useNavigate } from 'react-router-dom'
import { tokenStorage } from '@/utils/tokenStorage'
import { extractUserRolesFromToken, debugJwtToken } from '@/utils/jwtUtils'
import {
    setSessionSignedIn,
    setSessionUser,
    clearSession,
    setAuthenticated,
    setToken,
} from '@/store/slices/authSlice'
import {
    setAvailableBranches,
    clearBranchSelection,
    showBranchDialog,
} from '@/store/slices/branchSlice'
// import { selectHasSelectedBranch } from '@/store/selectors/branchSelector'
import { USER } from '@/constants/roles.constant'
import type {
    SignInCredential,
    AuthResult,
    User,
    Token,
    SignInResponse,
} from '@/@types/auth'
import type { ReactNode, Ref } from 'react'
import type { NavigateFunction } from 'react-router-dom'
import type { RootState, AppDispatch } from '@/store/store'
import ChangePasswordDialog from '@/components/shared/ChangePasswordDialog'

type AuthProviderProps = { children: ReactNode }

export type IsolatedNavigatorRef = {
    navigate: NavigateFunction
}

const IsolatedNavigator = ({ ref }: { ref: Ref<IsolatedNavigatorRef> }) => {
    const navigate = useNavigate()

    useImperativeHandle(ref, () => {
        return {
            navigate,
        }
    }, [navigate])

    return <></>
}

function AuthProvider({ children }: AuthProviderProps) {
    const dispatch = useDispatch<AppDispatch>()
    const signedIn = useSelector(
        (state: RootState) => state.auth.session.signedIn,
    )
    const user = useSelector((state: RootState) => state.auth.user)
    // const hasSelectedBranch = useSelector(selectHasSelectedBranch)
    const token = tokenStorage.getToken()

    // Debug logging
    console.log('AuthProvider Debug:', {
        token: !!token,
        signedIn,
        user: !!user,
    })

    const authenticated = Boolean(token) // Primary check is token existence - don't depend on signedIn

    // Restore session state from localStorage on app start
    useEffect(() => {
        const storedToken = tokenStorage.getToken()
        const storedAuthState = localStorage.getItem('redux-auth-state')

        if (storedToken && !signedIn) {
            console.log('Restoring session from localStorage...')
            // Restore session from localStorage
            if (storedAuthState) {
                try {
                    const authState = JSON.parse(storedAuthState)
                    dispatch(
                        setSessionSignedIn(authState.session?.signedIn || true),
                    )
                    dispatch(
                        setAuthenticated(authState.isAuthenticated || true),
                    )
                    if (authState.user) {
                        dispatch(setSessionUser(authState.user))
                    }
                } catch (error) {
                    console.error('Failed to restore auth state:', error)
                }
            }
        }
    }, [dispatch, signedIn])

    const navigatorRef = useRef<IsolatedNavigatorRef>(null)

    const redirect = () => {
        const search = window.location.search
        const params = new URLSearchParams(search)
        const redirectUrl = params.get(REDIRECT_URL_KEY)

        navigatorRef.current?.navigate(
            redirectUrl ? redirectUrl : appConfig.authenticatedEntryPath,
        )
    }

    const handleSignIn = (tokens: Token, userData?: SignInResponse) => {
        console.log(
            'Storing token:',
            tokens.accessToken ? 'Token present' : 'No token',
        )
        tokenStorage.setToken(tokens.accessToken || '')
        dispatch(setToken(tokens))
        dispatch(setSessionSignedIn(true))
        dispatch(setAuthenticated(true))

        if (userData) {
            dispatch(setSessionUser(userData))
        }
    }

    const handleSignOut = () => {
        tokenStorage.removeToken()
        dispatch(clearSession())
        dispatch(clearBranchSelection())
    }

    const signIn = async (values: SignInCredential): AuthResult => {
        try {
            const resp = await apiSignIn(values)
            if (resp && resp.token) {
                // Debug JWT token in development
                debugJwtToken(resp.token)

                // Extract roles from JWT token
                const userRoles = extractUserRolesFromToken(resp.token)

                // Convert SignInResponse to SignInResponse format
                const userData: SignInResponse = {
                    id: resp.id,
                    firstName: resp.firstName,
                    lastName: resp.lastName,
                    email: resp.email,
                    mustChangePassword: resp.mustChangePassword,
                    authority: userRoles,
                    token: resp.token,
                    refreshToken: resp.refreshToken,
                }

                // First, handle sign in to store the token
                handleSignIn(
                    {
                        accessToken: resp.token,
                        refreshToken: resp.refreshToken,
                    },
                    userData,
                )

                if (userData.mustChangePassword) {
                    const [isOpen, setIsOpen] = useState(true)

                    ;<ChangePasswordDialog
                        isOpen={isOpen}
                        onClose={() => {
                            setIsOpen(false)
                        }}
                    />
                }

                // Then fetch branches after token is stored
                if (userRoles.includes(USER)) {
                    try {
                        // Small delay to ensure token is properly stored
                        await new Promise((resolve) => setTimeout(resolve, 100))
                        const branches = await apiGetUserOrganization()
                        dispatch(setAvailableBranches(branches))
                        dispatch(showBranchDialog())
                    } catch (error) {
                        dispatch(showBranchDialog())
                    }
                } else {
                    redirect()
                }

                return {
                    status: 'success',
                    message: '',
                }
            }
            return {
                status: 'failed',
                message: 'Invalid response from server',
            }
            // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        } catch (errors: any) {
            return {
                status: 'failed',
                message: errors?.response?.data?.message || errors.toString(),
                code: errors?.response?.data?.code,
                description: errors?.response?.data?.description,
                headers: errors?.response?.headers,
            }
        }
    }

    const signOut = async () => {
        try {
            // await apiSignOut()
        } finally {
            handleSignOut()
            navigatorRef.current?.navigate(appConfig.unAuthenticatedEntryPath)
        }
    }

    return (
        <AuthContext.Provider
            value={{
                authenticated,
                user,
                signIn,
                signOut,
            }}
        >
            {children}
            <IsolatedNavigator ref={navigatorRef} />
        </AuthContext.Provider>
    )
}

export default AuthProvider
